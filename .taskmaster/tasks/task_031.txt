# Task ID: 31
# Title: Create Role-Based Layout System and Navigation Components
# Status: pending
# Dependencies: 2
# Priority: high
# Description: Implement a comprehensive layout system with role-specific navigation components (auth, admin, student, faculty) using Nuxt UI, including horizontal/vertical navigation, responsive sidebars, breadcrumbs, and mobile-optimized interfaces.
# Details:
## Implementation Details

1. **Create Base Layout Components**
   - Implement a `BaseLayout.vue` component that serves as the foundation for all role-specific layouts
   - Set up layout middleware to handle role-based access and routing
   - Create layout variants: `AuthLayout.vue`, `AdminLayout.vue`, `StudentLayout.vue`, and `FacultyLayout.vue`

2. **Horizontal Navigation Component**
   - Develop `UHorizontalNavigation.vue` using Nuxt UI components
   - Implement dynamic menu generation based on user role
   - Add active state styling and transitions
   - Example structure:
   ```vue
   <template>
     <div class="w-full bg-primary-50 dark:bg-primary-950 shadow-sm">
       <div class="container mx-auto px-4">
         <nav class="flex items-center justify-between h-16">
           <div class="flex items-center space-x-8">
             <NuxtLink to="/" class="font-bold text-xl">CMS</NuxtLink>
             <div v-for="item in navigationItems" :key="item.path">
               <NuxtLink :to="item.path" active-class="text-primary-600 font-medium">
                 {{ item.label }}
               </NuxtLink>
             </div>
           </div>
           <UserProfileDropdown />
         </nav>
       </div>
     </div>
   </template>
   ```

3. **Vertical Navigation Component**
   - Create `UVerticalNavigation.vue` for sidebar navigation
   - Implement collapsible sections for nested navigation
   - Add icons and visual indicators for each menu item
   - Support for pinned/unpinned states

4. **Responsive Sidebar Navigation**
   - Implement a responsive sidebar that transforms based on screen size
   - Create slide-in/out animations for mobile views
   - Add backdrop overlay for mobile navigation
   - Handle touch gestures for mobile interaction

5. **Breadcrumb Navigation**
   - Develop a breadcrumb component that dynamically updates based on route
   - Implement proper schema markup for SEO
   - Create a composable for generating breadcrumb data

6. **User Profile Dropdown**
   - Create a dropdown component for user profile actions
   - Include avatar, user info, theme toggle, and logout options
   - Implement proper focus management and keyboard navigation

7. **Theme Toggle Component**
   - Create a toggle for switching between light and dark themes
   - Persist theme preference in local storage
   - Add smooth transitions between theme changes

8. **Mobile Navigation**
   - Implement a hamburger menu for mobile views
   - Create a bottom navigation bar option for mobile
   - Ensure touch targets meet accessibility standards (minimum 44×44px)

9. **Role-Based Navigation Configuration**
   - Create a navigation configuration system that maps routes to roles
   - Example configuration:
   ```typescript
   // navigation.config.ts
   export const navigationConfig = {
     admin: [
       { label: 'Dashboard', path: '/admin', icon: 'i-heroicons-home' },
       { label: 'Users', path: '/admin/users', icon: 'i-heroicons-users' },
       // Additional admin routes
     ],
     student: [
       { label: 'Dashboard', path: '/student', icon: 'i-heroicons-home' },
       { label: 'Courses', path: '/student/courses', icon: 'i-heroicons-academic-cap' },
       // Additional student routes
     ],
     faculty: [
       { label: 'Dashboard', path: '/faculty', icon: 'i-heroicons-home' },
       { label: 'Classes', path: '/faculty/classes', icon: 'i-heroicons-academic-cap' },
       // Additional faculty routes
     ]
   }
   ```

10. **Navigation Composables**
    - Create a `useNavigation` composable for accessing navigation data
    - Implement logic for determining active navigation items
    - Add support for dynamic navigation updates

11. **Routing Integration**
    - Connect navigation components with Nuxt router
    - Implement navigation guards for role-based access control
    - Handle route transitions and loading states

12. **Accessibility Considerations**
    - Ensure all navigation components are keyboard navigable
    - Add proper ARIA attributes for screen readers
    - Implement focus management for modals and dropdowns
    - Test with screen readers and keyboard-only navigation

# Test Strategy:
## Testing Strategy

1. **Component Unit Tests**
   - Write unit tests for each navigation component using Vitest
   - Test rendering of navigation items based on different roles
   - Verify active state styling works correctly
   - Test responsive behavior by mocking different viewport sizes
   - Example test:
   ```typescript
   import { mount } from '@vue/test-utils'
   import { describe, it, expect } from 'vitest'
   import UHorizontalNavigation from '~/components/navigation/UHorizontalNavigation.vue'
   
   describe('UHorizontalNavigation', () => {
     it('renders correct navigation items for admin role', async () => {
       const wrapper = mount(UHorizontalNavigation, {
         props: { userRole: 'admin' }
       })
       expect(wrapper.findAll('a')).toHaveLength(expectedAdminNavItems.length)
       // Additional assertions
     })
   })
   ```

2. **Integration Tests**
   - Test navigation components within their respective layouts
   - Verify correct layout is loaded based on user role
   - Test navigation between routes and proper active state updates
   - Verify breadcrumb updates correctly when navigating

3. **Responsive Design Testing**
   - Test all navigation components across different viewport sizes:
     - Mobile (320px - 639px)
     - Tablet (640px - 1023px)
     - Desktop (1024px+)
   - Verify mobile navigation appears/disappears correctly
   - Test touch interactions on mobile devices

4. **Role-Based Access Testing**
   - Create test scenarios for each user role
   - Verify that navigation items are correctly filtered by role
   - Test unauthorized access attempts to role-specific routes
   - Verify redirects work correctly for unauthorized access

5. **Accessibility Testing**
   - Run automated accessibility tests using axe or similar tools
   - Test keyboard navigation through all menu items
   - Verify screen reader announcements for navigation changes
   - Check color contrast ratios for all navigation elements

6. **User Flow Testing**
   - Create end-to-end tests for common user flows:
     - Login → Dashboard navigation
     - Profile access and settings changes
     - Role-specific feature access
   - Test breadcrumb navigation for deep linking

7. **Theme Toggle Testing**
   - Verify theme toggle correctly switches between light and dark modes
   - Test theme persistence across page reloads
   - Check all navigation components render correctly in both themes

8. **Performance Testing**
   - Measure render time for navigation components
   - Test navigation performance on low-end devices
   - Verify smooth animations and transitions

9. **Browser Compatibility Testing**
   - Test navigation components across major browsers:
     - Chrome, Firefox, Safari, Edge
   - Verify consistent behavior and appearance

10. **Manual Testing Checklist**
    - Navigation renders correctly for each role
    - Mobile navigation is usable on touch devices
    - Keyboard navigation works for all interactive elements
    - Focus states are visible and follow a logical order
    - Screen readers can access all navigation options
    - Breadcrumbs accurately reflect current location
    - User profile dropdown functions correctly
    - Theme toggle works and persists settings
