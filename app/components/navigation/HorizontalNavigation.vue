<template>
  <nav class="bg-white dark:bg-gray-900 shadow-sm border-b border-gray-200 dark:border-gray-800">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center h-16">
        <!-- Logo and Brand -->
        <div class="flex items-center">
          <NuxtLink to="/" class="flex items-center space-x-2">
            <div class="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
              <UIcon name="i-heroicons-academic-cap" class="w-5 h-5 text-white" />
            </div>
            <span class="text-xl font-bold text-gray-900 dark:text-white">CMS</span>
          </NuxtLink>
        </div>

        <!-- Desktop Navigation -->
        <div class="hidden md:flex items-center space-x-8">
          <template v-for="item in navigationItems" :key="item.path">
            <!-- Simple navigation item -->
            <template v-if="!item.children">
              <NuxtLink
                :to="item.path"
                class="flex items-center space-x-1 px-3 py-2 rounded-md text-sm font-medium transition-colors"
                :class="[
                  isActiveRoute(item.path)
                    ? 'text-primary-600 bg-primary-50 dark:bg-primary-900/20 dark:text-primary-400'
                    : 'text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-800'
                ]"
              >
                <UIcon :name="item.icon" class="w-4 h-4" />
                <span>{{ item.label }}</span>
                <UBadge v-if="item.badge" size="xs" color="primary">{{ item.badge }}</UBadge>
              </NuxtLink>
            </template>

            <!-- Dropdown navigation item -->
            <template v-else>
              <UDropdown :items="[item.children]" :popper="{ placement: 'bottom-start' }">
                <UButton
                  variant="ghost"
                  color="gray"
                  :class="[
                    'flex items-center space-x-1',
                    hasActiveChild(item) ? 'text-primary-600 dark:text-primary-400' : ''
                  ]"
                >
                  <UIcon :name="item.icon" class="w-4 h-4" />
                  <span>{{ item.label }}</span>
                  <UIcon name="i-heroicons-chevron-down" class="w-3 h-3" />
                </UButton>
              </UDropdown>
            </template>
          </template>
        </div>

        <!-- Right side actions -->
        <div class="flex items-center space-x-4">
          <!-- Theme Toggle -->
          <UButton
            :icon="$colorMode.value === 'dark' ? 'i-heroicons-sun' : 'i-heroicons-moon'"
            color="gray"
            variant="ghost"
            size="sm"
            @click="$colorMode.preference = $colorMode.value === 'dark' ? 'light' : 'dark'"
          />

          <!-- User Profile Dropdown -->
          <UserProfileDropdown v-if="currentUser" />

          <!-- Mobile menu button -->
          <UButton
            icon="i-heroicons-bars-3"
            color="gray"
            variant="ghost"
            size="sm"
            class="md:hidden"
            @click="isMobileMenuOpen = !isMobileMenuOpen"
          />

          <!-- Login button for guests -->
          <UButton
            v-if="!currentUser"
            to="/auth/login"
            color="primary"
            size="sm"
          >
            Sign In
          </UButton>
        </div>
      </div>

      <!-- Mobile Navigation Menu -->
      <div v-if="isMobileMenuOpen" class="md:hidden border-t border-gray-200 dark:border-gray-700">
        <div class="px-2 pt-2 pb-3 space-y-1">
          <template v-for="item in navigationItems" :key="item.path">
            <!-- Simple mobile navigation item -->
            <template v-if="!item.children">
              <NuxtLink
                :to="item.path"
                class="flex items-center space-x-2 px-3 py-2 rounded-md text-base font-medium transition-colors"
                :class="[
                  isActiveRoute(item.path)
                    ? 'text-primary-600 bg-primary-50 dark:bg-primary-900/20 dark:text-primary-400'
                    : 'text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-800'
                ]"
                @click="isMobileMenuOpen = false"
              >
                <UIcon :name="item.icon" class="w-5 h-5" />
                <span>{{ item.label }}</span>
                <UBadge v-if="item.badge" size="xs" color="primary">{{ item.badge }}</UBadge>
              </NuxtLink>
            </template>

            <!-- Mobile dropdown items -->
            <template v-else>
              <div class="space-y-1">
                <div class="flex items-center space-x-2 px-3 py-2 text-base font-medium text-gray-900 dark:text-white">
                  <UIcon :name="item.icon" class="w-5 h-5" />
                  <span>{{ item.label }}</span>
                </div>
                <div class="pl-8 space-y-1">
                  <NuxtLink
                    v-for="child in item.children"
                    :key="child.path"
                    :to="child.path"
                    class="flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors"
                    :class="[
                      isActiveRoute(child.path)
                        ? 'text-primary-600 bg-primary-50 dark:bg-primary-900/20 dark:text-primary-400'
                        : 'text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-800'
                    ]"
                    @click="isMobileMenuOpen = false"
                  >
                    <UIcon :name="child.icon" class="w-4 h-4" />
                    <span>{{ child.label }}</span>
                  </NuxtLink>
                </div>
              </div>
            </template>
          </template>
        </div>
      </div>
    </div>
  </nav>
</template>

<script setup>
const { navigationItems, currentUser, isActiveRoute } = useNavigation()
const isMobileMenuOpen = ref(false)

const hasActiveChild = (item) => {
  if (!item.children) return false
  return item.children.some(child => isActiveRoute(child.path))
}

// Close mobile menu when route changes
watch(() => useRoute().path, () => {
  isMobileMenuOpen.value = false
})
</script>
