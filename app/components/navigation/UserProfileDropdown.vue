<template>
  <UDropdown :items="dropdownItems" :popper="{ placement: 'bottom-end' }">
    <UButton
      variant="ghost"
      color="gray"
      class="flex items-center space-x-2"
    >
      <UAvatar
        :src="currentUser?.avatar"
        :alt="currentUser?.name"
        size="sm"
      />
      <div class="hidden sm:block text-left">
        <div class="text-sm font-medium text-gray-900 dark:text-white">
          {{ currentUser?.name }}
        </div>
        <div class="text-xs text-gray-500 dark:text-gray-400 capitalize">
          {{ currentUser?.role }}
        </div>
      </div>
      <UIcon name="i-heroicons-chevron-down" class="w-3 h-3" />
    </UButton>
  </UDropdown>
</template>

<script setup>
const { currentUser, userRole, logout, switchRole } = useNavigation()

const dropdownItems = computed(() => [
  [
    {
      label: currentUser.value?.name || 'User',
      slot: 'account',
      disabled: true
    }
  ],
  [
    {
      label: 'Profile Settings',
      icon: 'i-heroicons-user-circle',
      click: () => navigateTo('/settings/profile')
    },
    {
      label: 'Preferences',
      icon: 'i-heroicons-cog-6-tooth',
      click: () => navigateTo('/settings/preferences')
    }
  ],
  [
    {
      label: 'Switch Role (Demo)',
      icon: 'i-heroicons-arrow-path',
      children: [
        {
          label: 'Admin',
          icon: 'i-heroicons-shield-check',
          click: () => switchRole('admin')
        },
        {
          label: 'Student',
          icon: 'i-heroicons-academic-cap',
          click: () => switchRole('student')
        },
        {
          label: 'Faculty',
          icon: 'i-heroicons-user-group',
          click: () => switchRole('faculty')
        }
      ]
    }
  ],
  [
    {
      label: 'Help & Support',
      icon: 'i-heroicons-question-mark-circle',
      click: () => navigateTo('/help')
    },
    {
      label: 'Documentation',
      icon: 'i-heroicons-document-text',
      click: () => window.open('/docs', '_blank')
    }
  ],
  [
    {
      label: 'Sign Out',
      icon: 'i-heroicons-arrow-right-on-rectangle',
      click: logout
    }
  ]
])
</script>
