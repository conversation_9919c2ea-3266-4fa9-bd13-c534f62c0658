<template>
  <aside
    class="fixed inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-800 transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0"
    :class="[
      isOpen ? 'translate-x-0' : '-translate-x-full',
      collapsed ? 'lg:w-16' : 'lg:w-64'
    ]"
  >
    <!-- Sidebar Header -->
    <div class="flex items-center justify-between h-16 px-4 border-b border-gray-200 dark:border-gray-800">
      <div v-if="!collapsed" class="flex items-center space-x-2">
        <div class="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
          <UIcon name="i-heroicons-academic-cap" class="w-5 h-5 text-white" />
        </div>
        <span class="text-xl font-bold text-gray-900 dark:text-white">CMS</span>
      </div>
      <div v-else class="flex justify-center w-full">
        <div class="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
          <UIcon name="i-heroicons-academic-cap" class="w-5 h-5 text-white" />
        </div>
      </div>
      
      <!-- Collapse toggle (desktop only) -->
      <UButton
        :icon="collapsed ? 'i-heroicons-chevron-right' : 'i-heroicons-chevron-left'"
        variant="ghost"
        color="gray"
        size="sm"
        class="hidden lg:flex"
        @click="collapsed = !collapsed"
      />
      
      <!-- Close button (mobile only) -->
      <UButton
        icon="i-heroicons-x-mark"
        variant="ghost"
        color="gray"
        size="sm"
        class="lg:hidden"
        @click="$emit('close')"
      />
    </div>

    <!-- Navigation Items -->
    <nav class="flex-1 px-2 py-4 space-y-1 overflow-y-auto">
      <template v-for="item in navigationItems" :key="item.path">
        <!-- Simple navigation item -->
        <template v-if="!item.children">
          <NuxtLink
            :to="item.path"
            class="group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors"
            :class="[
              isActiveRoute(item.path)
                ? 'bg-primary-100 text-primary-900 dark:bg-primary-900/20 dark:text-primary-100'
                : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-800 dark:hover:text-white'
            ]"
            :title="collapsed ? item.label : undefined"
          >
            <UIcon
              :name="item.icon"
              class="flex-shrink-0 w-5 h-5"
              :class="[
                isActiveRoute(item.path)
                  ? 'text-primary-600 dark:text-primary-400'
                  : 'text-gray-400 group-hover:text-gray-500 dark:group-hover:text-gray-300'
              ]"
            />
            <span v-if="!collapsed" class="ml-3">{{ item.label }}</span>
            <UBadge
              v-if="item.badge && !collapsed"
              size="xs"
              color="primary"
              class="ml-auto"
            >
              {{ item.badge }}
            </UBadge>
          </NuxtLink>
        </template>

        <!-- Collapsible navigation item -->
        <template v-else>
          <div>
            <button
              type="button"
              class="group w-full flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors"
              :class="[
                hasActiveChild(item)
                  ? 'bg-primary-100 text-primary-900 dark:bg-primary-900/20 dark:text-primary-100'
                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-800 dark:hover:text-white'
              ]"
              :title="collapsed ? item.label : undefined"
              @click="toggleSection(item.path)"
            >
              <UIcon
                :name="item.icon"
                class="flex-shrink-0 w-5 h-5"
                :class="[
                  hasActiveChild(item)
                    ? 'text-primary-600 dark:text-primary-400'
                    : 'text-gray-400 group-hover:text-gray-500 dark:group-hover:text-gray-300'
                ]"
              />
              <span v-if="!collapsed" class="ml-3 flex-1 text-left">{{ item.label }}</span>
              <UIcon
                v-if="!collapsed"
                name="i-heroicons-chevron-right"
                class="ml-3 w-4 h-4 transition-transform"
                :class="[
                  expandedSections.includes(item.path) ? 'rotate-90' : '',
                  hasActiveChild(item)
                    ? 'text-primary-600 dark:text-primary-400'
                    : 'text-gray-400 group-hover:text-gray-500'
                ]"
              />
            </button>

            <!-- Submenu -->
            <div
              v-if="!collapsed && expandedSections.includes(item.path)"
              class="mt-1 space-y-1"
            >
              <NuxtLink
                v-for="child in item.children"
                :key="child.path"
                :to="child.path"
                class="group flex items-center pl-8 pr-2 py-2 text-sm font-medium rounded-md transition-colors"
                :class="[
                  isActiveRoute(child.path)
                    ? 'bg-primary-100 text-primary-900 dark:bg-primary-900/20 dark:text-primary-100'
                    : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-800 dark:hover:text-white'
                ]"
              >
                <UIcon
                  :name="child.icon"
                  class="flex-shrink-0 w-4 h-4"
                  :class="[
                    isActiveRoute(child.path)
                      ? 'text-primary-600 dark:text-primary-400'
                      : 'text-gray-400 group-hover:text-gray-500 dark:group-hover:text-gray-300'
                  ]"
                />
                <span class="ml-3">{{ child.label }}</span>
              </NuxtLink>
            </div>
          </div>
        </template>
      </template>
    </nav>

    <!-- User Profile Section -->
    <div v-if="currentUser" class="border-t border-gray-200 dark:border-gray-800 p-4">
      <div v-if="!collapsed" class="flex items-center space-x-3">
        <UAvatar
          :src="currentUser.avatar"
          :alt="currentUser.name"
          size="sm"
        />
        <div class="flex-1 min-w-0">
          <p class="text-sm font-medium text-gray-900 dark:text-white truncate">
            {{ currentUser.name }}
          </p>
          <p class="text-xs text-gray-500 dark:text-gray-400 capitalize">
            {{ currentUser.role }}
          </p>
        </div>
      </div>
      <div v-else class="flex justify-center">
        <UAvatar
          :src="currentUser.avatar"
          :alt="currentUser.name"
          size="sm"
        />
      </div>
    </div>
  </aside>

  <!-- Backdrop for mobile -->
  <div
    v-if="isOpen"
    class="fixed inset-0 z-40 bg-gray-600 bg-opacity-75 lg:hidden"
    @click="$emit('close')"
  />
</template>

<script setup>
interface Props {
  isOpen?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isOpen: false
})

const emit = defineEmits<{
  close: []
}>()

const { navigationItems, currentUser, isActiveRoute } = useNavigation()

const collapsed = ref(false)
const expandedSections = ref<string[]>([])

const hasActiveChild = (item) => {
  if (!item.children) return false
  return item.children.some(child => isActiveRoute(child.path))
}

const toggleSection = (path: string) => {
  if (collapsed.value) {
    // In collapsed mode, navigate to the parent path
    navigateTo(path)
    return
  }
  
  const index = expandedSections.value.indexOf(path)
  if (index > -1) {
    expandedSections.value.splice(index, 1)
  } else {
    expandedSections.value.push(path)
  }
}

// Auto-expand sections with active children
watchEffect(() => {
  navigationItems.value.forEach(item => {
    if (item.children && hasActiveChild(item) && !expandedSections.value.includes(item.path)) {
      expandedSections.value.push(item.path)
    }
  })
})

// Collapse sidebar when switching to mobile
const { width } = useWindowSize()
watch(width, (newWidth) => {
  if (newWidth < 1024) {
    collapsed.value = false
  }
})
</script>
