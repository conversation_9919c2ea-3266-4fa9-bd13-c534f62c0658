<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Vertical Navigation Sidebar -->
    <VerticalNavigation
      :is-open="isSidebarOpen"
      @close="isSidebarOpen = false"
    />

    <!-- Main Content Area -->
    <div class="lg:pl-64">
      <!-- Top Navigation Bar -->
      <div class="sticky top-0 z-40 bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between h-16 px-4 sm:px-6 lg:px-8">
          <!-- Mobile menu button -->
          <UButton
            icon="i-heroicons-bars-3"
            color="gray"
            variant="ghost"
            size="sm"
            class="lg:hidden"
            @click="isSidebarOpen = true"
          />

          <!-- Page Title -->
          <div class="flex-1 min-w-0">
            <h1 class="text-lg font-semibold text-gray-900 dark:text-white truncate">
              {{ pageTitle }}
            </h1>
          </div>

          <!-- Right side actions -->
          <div class="flex items-center space-x-4">
            <!-- Notifications -->
            <UButton
              icon="i-heroicons-bell"
              color="gray"
              variant="ghost"
              size="sm"
              :badge="notificationCount > 0 ? notificationCount : undefined"
            />

            <!-- Theme Toggle -->
            <UButton
              :icon="$colorMode.value === 'dark' ? 'i-heroicons-sun' : 'i-heroicons-moon'"
              color="gray"
              variant="ghost"
              size="sm"
              @click="$colorMode.preference = $colorMode.value === 'dark' ? 'light' : 'dark'"
            />

            <!-- User Profile Dropdown -->
            <UserProfileDropdown />
          </div>
        </div>
      </div>

      <!-- Page Content -->
      <main class="p-4 sm:p-6 lg:p-8">
        <!-- Breadcrumb Navigation -->
        <div class="mb-6">
          <BreadcrumbNavigation />
        </div>

        <!-- Page Content Slot -->
        <slot />
      </main>
    </div>
  </div>
</template>

<script setup>
const route = useRoute()
const isSidebarOpen = ref(false)

// Mock notification count
const notificationCount = ref(3)

// Generate page title from route
const pageTitle = computed(() => {
  const segments = route.path.split('/').filter(Boolean)
  if (segments.length === 0) return 'Dashboard'
  
  const lastSegment = segments[segments.length - 1]
  return lastSegment
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ')
})

// Close sidebar when route changes on mobile
watch(() => route.path, () => {
  isSidebarOpen.value = false
})
</script>
