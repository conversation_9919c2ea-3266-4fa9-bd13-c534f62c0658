<template>
  <!-- <header class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <div class="flex items-center">
            <h1 class="text-xl font-semibold text-gray-900 dark:text-white">
              College Management System
            </h1>
          </div>
          <div class="flex items-center space-x-4">
            <UButton
              :icon="$colorMode.value === 'dark' ? 'i-heroicons-sun' : 'i-heroicons-moon'"
              color="gray"
              variant="ghost"
              size="sm"
              @click="$colorMode.preference = $colorMode.value === 'dark' ? 'light' : 'dark'"
            />
          </div>
        </div>
      </div>
    </header> -->

  <!-- Main Content -->
  <main>
    <slot />
  </main>

  <!-- Footer -->
  <!-- <footer class="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 mt-auto">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
      <p class="text-center text-sm text-gray-500 dark:text-gray-400">
        © 2025 College Management System. All rights reserved.
      </p>
    </div>
  </footer> -->
</template>

<!-- <script setup>
// Enable color mode
// const colorMode = useColorMode()
</script> -->
