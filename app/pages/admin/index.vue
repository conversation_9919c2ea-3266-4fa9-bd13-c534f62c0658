<template>
  <div class="space-y-6">
    <!-- <PERSON> Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Admin Dashboard</h1>
        <p class="text-gray-600 dark:text-gray-300">Welcome back, Administrator</p>
      </div>
      <UButton color="primary" icon="i-heroicons-plus">
        Quick Action
      </UButton>
    </div>

    <!-- Stats Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <UCard>
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
              <UIcon name="i-heroicons-users" class="w-5 h-5 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-300">Total Students</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">2,847</p>
          </div>
        </div>
      </UCard>

      <UCard>
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
              <UIcon name="i-heroicons-user-group" class="w-5 h-5 text-green-600 dark:text-green-400" />
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-300">Faculty Members</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">156</p>
          </div>
        </div>
      </UCard>

      <UCard>
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg flex items-center justify-center">
              <UIcon name="i-heroicons-academic-cap" class="w-5 h-5 text-yellow-600 dark:text-yellow-400" />
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-300">Active Courses</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">342</p>
          </div>
        </div>
      </UCard>

      <UCard>
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
              <UIcon name="i-heroicons-currency-dollar" class="w-5 h-5 text-purple-600 dark:text-purple-400" />
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-300">Revenue</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">$2.4M</p>
          </div>
        </div>
      </UCard>
    </div>

    <!-- Quick Actions -->
    <UCard>
      <template #header>
        <h2 class="text-lg font-semibold">Quick Actions</h2>
      </template>
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
        <UButton
          to="/admin/users"
          variant="outline"
          class="justify-start"
          icon="i-heroicons-users"
        >
          Manage Users
        </UButton>
        <UButton
          to="/admin/academic/courses"
          variant="outline"
          class="justify-start"
          icon="i-heroicons-book-open"
        >
          Course Catalog
        </UButton>
        <UButton
          to="/admin/financial/budget"
          variant="outline"
          class="justify-start"
          icon="i-heroicons-chart-pie"
        >
          Budget Overview
        </UButton>
        <UButton
          to="/admin/analytics"
          variant="outline"
          class="justify-start"
          icon="i-heroicons-chart-bar"
        >
          Analytics
        </UButton>
      </div>
    </UCard>

    <!-- Recent Activity -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <UCard>
        <template #header>
          <h2 class="text-lg font-semibold">Recent Activity</h2>
        </template>
        <div class="space-y-4">
          <div class="flex items-center space-x-3">
            <UAvatar size="sm" src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&auto=format&fit=facepad&facepad=2&w=256&h=256&q=80" />
            <div class="flex-1 min-w-0">
              <p class="text-sm font-medium text-gray-900 dark:text-white">John Doe</p>
              <p class="text-sm text-gray-500 dark:text-gray-400">Registered for CS 101</p>
            </div>
            <span class="text-xs text-gray-400">2m ago</span>
          </div>
          <div class="flex items-center space-x-3">
            <UAvatar size="sm" src="https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-1.2.1&auto=format&fit=facepad&facepad=2&w=256&h=256&q=80" />
            <div class="flex-1 min-w-0">
              <p class="text-sm font-medium text-gray-900 dark:text-white">Jane Smith</p>
              <p class="text-sm text-gray-500 dark:text-gray-400">Updated course syllabus</p>
            </div>
            <span class="text-xs text-gray-400">5m ago</span>
          </div>
          <div class="flex items-center space-x-3">
            <UAvatar size="sm" src="https://images.unsplash.com/photo-1519244703995-f4e0f30006d5?ixlib=rb-1.2.1&auto=format&fit=facepad&facepad=2&w=256&h=256&q=80" />
            <div class="flex-1 min-w-0">
              <p class="text-sm font-medium text-gray-900 dark:text-white">Mike Johnson</p>
              <p class="text-sm text-gray-500 dark:text-gray-400">Submitted grade report</p>
            </div>
            <span class="text-xs text-gray-400">10m ago</span>
          </div>
        </div>
      </UCard>

      <UCard>
        <template #header>
          <h2 class="text-lg font-semibold">System Alerts</h2>
        </template>
        <div class="space-y-3">
          <UAlert
            icon="i-heroicons-exclamation-triangle"
            color="yellow"
            variant="soft"
            title="Server Maintenance"
            description="Scheduled maintenance on Sunday 2 AM - 4 AM"
          />
          <UAlert
            icon="i-heroicons-information-circle"
            color="blue"
            variant="soft"
            title="New Feature"
            description="Mobile app v2.0 is now available"
          />
          <UAlert
            icon="i-heroicons-check-circle"
            color="green"
            variant="soft"
            title="Backup Complete"
            description="Daily backup completed successfully"
          />
        </div>
      </UCard>
    </div>
  </div>
</template>

<script setup>
definePageMeta({
  layout: 'admin'
})
</script>
