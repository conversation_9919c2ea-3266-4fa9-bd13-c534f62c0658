<template>
  <div class="space-y-6">
    <!-- Welcome Header -->
    <div class="bg-gradient-to-r from-primary-500 to-blue-600 rounded-lg p-6 text-white">
      <h1 class="text-2xl font-bold mb-2">Welcome back, <PERSON>!</h1>
      <p class="text-primary-100">Ready to continue your academic journey?</p>
    </div>

    <!-- Current Semester Overview -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Enrolled Courses -->
      <div class="lg:col-span-2">
        <UCard>
          <template #header>
            <div class="flex items-center justify-between">
              <h2 class="text-lg font-semibold">Current Courses</h2>
              <UButton to="/student/courses/enrolled" variant="ghost" size="sm">
                View All
              </UButton>
            </div>
          </template>
          <div class="space-y-4">
            <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                  <UIcon name="i-heroicons-code-bracket" class="w-5 h-5 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <h3 class="font-medium text-gray-900 dark:text-white">CS 101 - Intro to Programming</h3>
                  <p class="text-sm text-gray-500 dark:text-gray-400">Prof. Smith • MWF 10:00 AM</p>
                </div>
              </div>
              <UBadge color="green" variant="soft">A-</UBadge>
            </div>
            
            <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
                  <UIcon name="i-heroicons-calculator" class="w-5 h-5 text-green-600 dark:text-green-400" />
                </div>
                <div>
                  <h3 class="font-medium text-gray-900 dark:text-white">MATH 201 - Calculus II</h3>
                  <p class="text-sm text-gray-500 dark:text-gray-400">Prof. Johnson • TTh 2:00 PM</p>
                </div>
              </div>
              <UBadge color="blue" variant="soft">B+</UBadge>
            </div>
            
            <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
                  <UIcon name="i-heroicons-book-open" class="w-5 h-5 text-purple-600 dark:text-purple-400" />
                </div>
                <div>
                  <h3 class="font-medium text-gray-900 dark:text-white">ENG 102 - Composition</h3>
                  <p class="text-sm text-gray-500 dark:text-gray-400">Prof. Davis • MW 1:00 PM</p>
                </div>
              </div>
              <UBadge color="yellow" variant="soft">B</UBadge>
            </div>
          </div>
        </UCard>
      </div>

      <!-- Academic Progress -->
      <div class="space-y-6">
        <UCard>
          <template #header>
            <h2 class="text-lg font-semibold">Academic Progress</h2>
          </template>
          <div class="space-y-4">
            <div>
              <div class="flex justify-between text-sm mb-1">
                <span class="text-gray-600 dark:text-gray-300">Degree Progress</span>
                <span class="font-medium">45%</span>
              </div>
              <UProgress :value="45" color="primary" />
            </div>
            
            <div>
              <div class="flex justify-between text-sm mb-1">
                <span class="text-gray-600 dark:text-gray-300">Credits Completed</span>
                <span class="font-medium">54/120</span>
              </div>
              <UProgress :value="45" color="green" />
            </div>
            
            <div class="pt-4 border-t border-gray-200 dark:border-gray-700">
              <div class="text-center">
                <div class="text-2xl font-bold text-primary-600 dark:text-primary-400">3.8</div>
                <div class="text-sm text-gray-500 dark:text-gray-400">Cumulative GPA</div>
              </div>
            </div>
          </div>
        </UCard>

        <UCard>
          <template #header>
            <h2 class="text-lg font-semibold">Quick Actions</h2>
          </template>
          <div class="space-y-2">
            <UButton
              to="/student/courses/registration"
              block
              variant="outline"
              icon="i-heroicons-plus-circle"
            >
              Course Registration
            </UButton>
            <UButton
              to="/student/grades"
              block
              variant="outline"
              icon="i-heroicons-chart-bar-square"
            >
              View Grades
            </UButton>
            <UButton
              to="/student/financial-aid"
              block
              variant="outline"
              icon="i-heroicons-currency-dollar"
            >
              Financial Aid
            </UButton>
          </div>
        </UCard>
      </div>
    </div>

    <!-- Upcoming Assignments & Announcements -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <UCard>
        <template #header>
          <h2 class="text-lg font-semibold">Upcoming Assignments</h2>
        </template>
        <div class="space-y-3">
          <div class="flex items-center justify-between p-3 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
            <div>
              <h3 class="font-medium text-gray-900 dark:text-white">Programming Project 2</h3>
              <p class="text-sm text-gray-500 dark:text-gray-400">CS 101</p>
            </div>
            <div class="text-right">
              <div class="text-sm font-medium text-red-600 dark:text-red-400">Due Tomorrow</div>
              <div class="text-xs text-gray-500">11:59 PM</div>
            </div>
          </div>
          
          <div class="flex items-center justify-between p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
            <div>
              <h3 class="font-medium text-gray-900 dark:text-white">Calculus Problem Set 5</h3>
              <p class="text-sm text-gray-500 dark:text-gray-400">MATH 201</p>
            </div>
            <div class="text-right">
              <div class="text-sm font-medium text-yellow-600 dark:text-yellow-400">Due Friday</div>
              <div class="text-xs text-gray-500">5:00 PM</div>
            </div>
          </div>
          
          <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div>
              <h3 class="font-medium text-gray-900 dark:text-white">Essay Draft</h3>
              <p class="text-sm text-gray-500 dark:text-gray-400">ENG 102</p>
            </div>
            <div class="text-right">
              <div class="text-sm font-medium text-gray-600 dark:text-gray-300">Due Next Week</div>
              <div class="text-xs text-gray-500">Monday</div>
            </div>
          </div>
        </div>
      </UCard>

      <UCard>
        <template #header>
          <h2 class="text-lg font-semibold">Recent Announcements</h2>
        </template>
        <div class="space-y-3">
          <UAlert
            icon="i-heroicons-megaphone"
            color="blue"
            variant="soft"
            title="Registration Opens"
            description="Spring 2025 course registration begins Monday, Nov 15th"
          />
          <UAlert
            icon="i-heroicons-calendar-days"
            color="green"
            variant="soft"
            title="Study Week"
            description="Final exam study week starts December 5th"
          />
          <UAlert
            icon="i-heroicons-book-open"
            color="purple"
            variant="soft"
            title="Library Hours"
            description="Extended hours during finals week"
          />
        </div>
      </UCard>
    </div>
  </div>
</template>

<script setup>
definePageMeta({
  layout: 'student'
})
</script>
